// Presentation JavaScript
let currentSlide = 1;
const totalSlides = 3;

// Initialize presentation
document.addEventListener('DOMContentLoaded', function() {
    // Show current slide indicator in console
    console.log(`Current slide: ${currentSlide} of ${totalSlides}`);
});

// Navigation functions
function nextSlide() {
    if (currentSlide < totalSlides) {
        goToSlide(currentSlide + 1);
    }
}

function previousSlide() {
    if (currentSlide > 1) {
        goToSlide(currentSlide - 1);
    }
}

function goToSlide(slideNumber) {
    if (slideNumber >= 1 && slideNumber <= totalSlides) {
        // Hide current slide
        document.getElementById(`slide-${currentSlide}`).classList.remove('active');

        // Show new slide
        document.getElementById(`slide-${slideNumber}`).classList.add('active');

        // Update current slide number
        currentSlide = slideNumber;

        // Log current slide
        console.log(`Current slide: ${currentSlide} of ${totalSlides}`);
    }
}

// Navigation functions removed - keyboard only navigation

// Keyboard navigation
document.addEventListener('keydown', function(event) {
    switch(event.key) {
        case 'ArrowRight':
        case ' ':
            event.preventDefault();
            nextSlide();
            break;
        case 'ArrowLeft':
            event.preventDefault();
            previousSlide();
            break;
        case 'Home':
            event.preventDefault();
            goToSlide(1);
            break;
        case 'End':
            event.preventDefault();
            goToSlide(totalSlides);
            break;
        case 'Escape':
            event.preventDefault();
            toggleFullscreen();
            break;
    }
});

// Touch/swipe support for mobile
let touchStartX = 0;
let touchEndX = 0;

document.addEventListener('touchstart', function(event) {
    touchStartX = event.changedTouches[0].screenX;
});

document.addEventListener('touchend', function(event) {
    touchEndX = event.changedTouches[0].screenX;
    handleSwipe();
});

function handleSwipe() {
    const swipeThreshold = 50;
    const swipeDistance = touchEndX - touchStartX;
    
    if (Math.abs(swipeDistance) > swipeThreshold) {
        if (swipeDistance > 0) {
            // Swipe right - go to previous slide
            previousSlide();
        } else {
            // Swipe left - go to next slide
            nextSlide();
        }
    }
}

// Fullscreen functionality
function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen().catch(err => {
            console.log(`Error attempting to enable fullscreen: ${err.message}`);
        });
    } else {
        document.exitFullscreen();
    }
}

// Auto-advance functionality (optional)
let autoAdvanceInterval;
let isAutoAdvancing = false;

function startAutoAdvance(intervalMs = 10000) {
    if (!isAutoAdvancing) {
        isAutoAdvancing = true;
        autoAdvanceInterval = setInterval(() => {
            if (currentSlide < totalSlides) {
                nextSlide();
            } else {
                stopAutoAdvance();
            }
        }, intervalMs);
    }
}

function stopAutoAdvance() {
    if (isAutoAdvancing) {
        clearInterval(autoAdvanceInterval);
        isAutoAdvancing = false;
    }
}

// Stop auto-advance on user interaction
document.addEventListener('click', stopAutoAdvance);
document.addEventListener('keydown', stopAutoAdvance);
document.addEventListener('touchstart', stopAutoAdvance);

// Presentation controls (optional - can be activated via console)
function presentationControls() {
    console.log('Presentation Controls:');
    console.log('- nextSlide(): Go to next slide');
    console.log('- previousSlide(): Go to previous slide');
    console.log('- goToSlide(n): Go to slide number n');
    console.log('- startAutoAdvance(ms): Start auto-advance with interval');
    console.log('- stopAutoAdvance(): Stop auto-advance');
    console.log('- toggleFullscreen(): Toggle fullscreen mode');
    console.log('');
    console.log('Keyboard shortcuts:');
    console.log('- Arrow Right / Space: Next slide');
    console.log('- Arrow Left: Previous slide');
    console.log('- Home: First slide');
    console.log('- End: Last slide');
    console.log('- Escape: Toggle fullscreen');
}

// Initialize presentation controls info
presentationControls();

// Smooth scrolling for long content
document.querySelectorAll('.slide').forEach(slide => {
    slide.style.scrollBehavior = 'smooth';
});

// Add loading animation
window.addEventListener('load', function() {
    document.body.classList.add('loaded');
});

// Print functionality
function printPresentation() {
    window.print();
}

// Export functionality (basic)
function exportAsHTML() {
    const htmlContent = document.documentElement.outerHTML;
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'spark-presentation.html';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// Make functions globally available
window.nextSlide = nextSlide;
window.previousSlide = previousSlide;
window.goToSlide = goToSlide;
window.startAutoAdvance = startAutoAdvance;
window.stopAutoAdvance = stopAutoAdvance;
window.toggleFullscreen = toggleFullscreen;
window.printPresentation = printPresentation;
window.exportAsHTML = exportAsHTML;
